
import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Copy, Check, ArrowLeft, ArrowRight, Send, Phone, Mail, User, CreditCard } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

const formSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  phoneNumber: z.string().regex(/^7\d{8}$/, 'Phone number must be 9 digits starting with 7'),
  email: z.string().email('Invalid email address'),
  course: z.enum(['makeup_masterclass', 'hair_styling_masterclass', 'complete_package'], {
    required_error: 'Please select a course',
  }),
  mpesaName: z.string().min(2, 'MPesa payer name is required'),
  mpesaNumber: z.string().min(10, 'MPesa number is required'),
  mpesaMessage: z.string().min(10, 'MPesa confirmation message is required'),
});

type FormData = z.infer<typeof formSchema>;

const COURSES = [
  {
    id: 'makeup_masterclass' as const,
    name: 'Make Up Masterclass',
    price: 10000,
    description: 'Professional makeup techniques and artistry'
  },
  {
    id: 'hair_styling_masterclass' as const,
    name: 'Hair Styling Masterclass',
    price: 10000,
    description: 'Advanced hair styling and treatment methods'
  },
  {
    id: 'complete_package' as const,
    name: 'Complete Package (Make Up + Hair)',
    price: 20000,
    description: 'Comprehensive beauty training package'
  }
];

export const CourseRegistration = () => {
  const [step, setStep] = useState(1);
  const [copied, setCopied] = useState(false);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: '',
      phoneNumber: '',
      email: '',
      course: undefined,
      mpesaName: '',
      mpesaNumber: '',
      mpesaMessage: '',
    },
  });

  const selectedCourse = form.watch('course');
  const courseDetails = COURSES.find(c => c.id === selectedCourse);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    toast({
      title: "Copied!",
      description: "Till number copied to clipboard",
    });
  };

  const handleNext = async () => {
    const isValid = await form.trigger(['fullName', 'phoneNumber', 'email', 'course']);
    if (isValid) {
      setStep(2);
    }
  };

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      // Prepare data for submission
      const registrationData = {
        full_name: data.fullName,
        phone_number: `+254${data.phoneNumber}`,
        email: data.email,
        course: data.course,
        amount: courseDetails?.price || 0,
        mpesa_name: data.mpesaName,
        mpesa_number: data.mpesaNumber,
        mpesa_message: data.mpesaMessage,
        status: 'pending' as const
      };

      console.log('Submitting registration:', registrationData);

      // Insert into database
      const { data: result, error } = await supabase
        .from('registrations')
        .insert([registrationData])
        .select()
        .single();

      if (error) throw error;

      console.log('Registration successful:', result);

      // Send acknowledgment email
      await supabase.functions.invoke('send-registration-email', {
        body: {
          email: data.email,
          fullName: data.fullName,
          course: courseDetails?.name || '',
          type: 'acknowledgment'
        }
      });

      toast({
        title: "Registration Successful! ✨",
        description: "We've received your registration. Check your email for confirmation.",
      });

      // Reset form
      form.reset();
      setStep(1);

    } catch (error) {
      console.error('Registration error:', error);
      toast({
        title: "Registration Failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-hero py-12 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold text-white drop-shadow-lg mb-4 tracking-wide">
            JOIN SHATAFRIKA BEAUTY MASTERCLASS
          </h1>

          {/* Logo */}
          <div className="flex justify-center mb-6">
            <img
              src="/shatafrika-logo.svg"
              alt="ShatAfrika Beauty Masterclass Logo"
              className="w-80 h-auto max-w-full"
            />
          </div>

          <p className="text-xl text-white/90 font-medium drop-shadow-md">
            Professional Beauty Training & Certification
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
              step >= 1 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
            }`}>
              1
            </div>
            <div className={`h-1 w-16 ${step >= 2 ? 'bg-primary' : 'bg-muted'}`} />
            <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
              step >= 2 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
            }`}>
              2
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            {step === 1 && (
              <Card className="bg-gradient-card shadow-strong">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="w-5 h-5 mr-2" />
                    Personal Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number *</FormLabel>
                        <FormControl>
                          <div className="flex">
                            <div className="flex items-center px-3 bg-muted border border-r-0 border-input rounded-l-md text-sm text-muted-foreground">
                              +254
                            </div>
                            <Input 
                              placeholder="7XXXXXXXX" 
                              className="rounded-l-none border-l-0" 
                              {...field} 
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address *</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="course"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Select Course *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Choose your course" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {COURSES.map((course) => (
                              <SelectItem key={course.id} value={course.id}>
                                <div className="flex items-center justify-between w-full">
                                  <div>
                                    <div className="font-medium">{course.name}</div>
                                    <div className="text-sm text-muted-foreground">{course.description}</div>
                                  </div>
                                  <Badge variant="secondary" className="ml-2">
                                    KSH {course.price.toLocaleString()}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {courseDetails && (
                    <div className="p-4 bg-accent/10 border border-accent/20 rounded-lg">
                      <h4 className="font-semibold text-accent mb-2">Selected Course:</h4>
                      <p className="text-sm">{courseDetails.name}</p>
                      <p className="text-sm text-muted-foreground">{courseDetails.description}</p>
                      <p className="text-lg font-bold text-accent mt-2">
                        Total: KSH {courseDetails.price.toLocaleString()}
                      </p>
                    </div>
                  )}

                  <Button 
                    type="button" 
                    onClick={handleNext}
                    className="w-full"
                    disabled={!courseDetails}
                  >
                    Next: Payment Instructions
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            )}

            {step === 2 && (
              <div className="space-y-6">
                {/* Payment Instructions */}
                <Card className="bg-gradient-card shadow-strong">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <CreditCard className="w-5 h-5 mr-2" />
                      MPesa Payment Instructions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-6 bg-primary/5 border border-primary/20 rounded-lg">
                      <h4 className="font-semibold text-primary mb-4">Follow these steps:</h4>
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        <li>Go to MPesa on your phone</li>
                        <li>Select "Lipa na M-Pesa"</li>
                        <li>Select "Buy Goods and Services"</li>
                        <li>Enter Till Number: <strong>6896544</strong></li>
                        <li>Enter Amount: <strong>KSH {courseDetails?.price.toLocaleString()}</strong></li>
                        <li>Enter your PIN and confirm</li>
                      </ol>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border rounded-lg">
                        <Label className="text-sm font-medium text-muted-foreground">Till Number</Label>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-2xl font-bold">6896544</span>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard('6896544')}
                          >
                            {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                          </Button>
                        </div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <Label className="text-sm font-medium text-muted-foreground">Business Name</Label>
                        <div className="mt-1">
                          <span className="text-sm font-medium">MAUREEN WANZA MULINGE</span>
                        </div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <Label className="text-sm font-medium text-muted-foreground">Amount to Pay</Label>
                        <div className="mt-1">
                          <span className="text-xl font-bold text-primary">
                            KSH {courseDetails?.price.toLocaleString()}
                          </span>
                        </div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <Label className="text-sm font-medium text-muted-foreground">Course</Label>
                        <div className="mt-1">
                          <span className="text-sm font-medium">{courseDetails?.name}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Payment Confirmation Form */}
                <Card className="bg-gradient-card shadow-strong">
                  <CardHeader>
                    <CardTitle>Payment Confirmation</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      After making payment, please provide the following details:
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="mpesaName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>MPesa Payer Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="Name that appears on MPesa confirmation" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="mpesaNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>MPesa Phone Number *</FormLabel>
                          <FormControl>
                            <Input placeholder="Phone number used for payment" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="mpesaMessage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>MPesa Confirmation Message *</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Paste the full MPesa confirmation SMS here..."
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex space-x-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setStep(1)}
                        className="w-full"
                      >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back
                      </Button>
                      
                      <Button
                        type="submit"
                        className="w-full"
                        disabled={loading}
                      >
                        {loading ? (
                          "Submitting..."
                        ) : (
                          <>
                            <Send className="w-4 h-4 mr-2" />
                            Submit Registration
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </form>
        </Form>
      </div>
    </div>
  );
};

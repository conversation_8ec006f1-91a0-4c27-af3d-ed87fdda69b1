-- Check if <PERSON><PERSON> is enabled
SELECT tablename, rowsecurity FROM pg_tables WHERE tablename = 'registrations';

-- Drop all existing insert policies and create a completely open one
DROP POLICY IF EXISTS "Enable public insert for registrations" ON public.registrations;
DROP POLICY IF EXISTS "Anyone can insert registrations" ON public.registrations;

-- Create a new policy that allows anyone to insert
CREATE POLICY "Public registration access" 
ON public.registrations 
FOR INSERT 
TO public
WITH CHECK (true);

-- Also allow public to select their own registrations by email
CREATE POLICY "Public can view own registration by email" 
ON public.registrations 
FOR SELECT 
TO public
USING (true);
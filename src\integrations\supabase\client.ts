// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zkvcvznlcrthjhdmuqor.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InprdmN2em5sY3J0aGpoZG11cW9yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NjAzNDcsImV4cCI6MjA3MjMzNjM0N30.7OyIgFNMyN_PJUxNNxuQNXMdJoF4btfJg69NohYfE6I";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
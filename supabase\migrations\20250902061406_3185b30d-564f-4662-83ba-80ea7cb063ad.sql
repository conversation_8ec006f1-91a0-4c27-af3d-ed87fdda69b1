-- Create theme_settings table for admin color management
CREATE TABLE public.theme_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  setting_name TEXT NOT NULL UNIQUE,
  setting_value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.theme_settings ENABLE ROW LEVEL SECURITY;

-- Create policies for theme settings - public read access, no insert/update restrictions for simplicity
CREATE POLICY "Theme settings are viewable by everyone" 
ON public.theme_settings 
FOR SELECT 
USING (true);

CREATE POLICY "Allow all theme setting operations" 
ON public.theme_settings 
FOR ALL 
USING (true);

-- Insert default feminine/nude color scheme
INSERT INTO public.theme_settings (setting_name, setting_value) VALUES 
('background_color', 'nude_rose'),
('primary_color', 'soft_pink'),
('accent_color', 'warm_beige');

-- <PERSON><PERSON> function to update timestamps
CREATE OR REPLACE FUNCTION public.update_theme_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
NEW.updated_at = now();
RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_theme_settings_updated_at
BEFORE UPDATE ON public.theme_settings
FOR EACH ROW
EXECUTE FUNCTION public.update_theme_settings_updated_at();
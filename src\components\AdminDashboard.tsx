
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Shield, Eye, CheckCircle, Clock, Users, DollarSign, Mail, Phone, Download, Send } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface Registration {
  id: string;
  full_name: string;
  email: string;
  phone_number: string;
  course: string;
  amount: number;
  mpesa_name: string;
  mpesa_number: string;
  mpesa_message: string;
  status: 'pending' | 'verified' | 'rejected';
  created_at: string;
  verified_at: string | null;
}

const ADMIN_PASSWORD = 're_4fmn1EeW_N5t2AM1CkbCZHCfHvpRAJwq6';

const COURSE_NAMES = {
  makeup_masterclass: 'Make Up Masterclass',
  hair_styling_masterclass: 'Hair Styling Masterclass',
  complete_package: 'Complete Package (Make Up + Hair)'
};

export const AdminDashboard = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRegistration, setSelectedRegistration] = useState<Registration | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showBulkEmail, setShowBulkEmail] = useState(false);
  const [showIndividualEmail, setShowIndividualEmail] = useState(false);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailMessage, setEmailMessage] = useState('');
  const { toast } = useToast();

  const handleLogin = () => {
    if (password === ADMIN_PASSWORD) {
      setIsAuthenticated(true);
      fetchRegistrations();
    } else {
      toast({
        title: "Access Denied",
        description: "Invalid password",
        variant: "destructive"
      });
    }
  };

  const fetchRegistrations = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('admin-data', {
        body: {
          password: ADMIN_PASSWORD,
          action: 'fetch'
        }
      });

      if (error) throw error;
      if (data.error) throw new Error(data.error);
      
      setRegistrations(data.data || []);
    } catch (error) {
      console.error('Error fetching registrations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch registrations",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (registrationId: string, newStatus: 'pending' | 'verified' | 'rejected') => {
    try {
      // Update status using admin function
      const { data, error } = await supabase.functions.invoke('admin-data', {
        body: {
          password: ADMIN_PASSWORD,
          action: 'update',
          registrationId,
          status: newStatus
        }
      });

      if (error) throw error;
      if (data.error) throw new Error(data.error);

      // Send verification email if status is verified
      if (newStatus === 'verified') {
        const registration = registrations.find(r => r.id === registrationId);
        if (registration) {
          await supabase.functions.invoke('send-registration-email', {
            body: {
              email: registration.email,
              fullName: registration.full_name,
              course: COURSE_NAMES[registration.course as keyof typeof COURSE_NAMES],
              type: 'verification'
            }
          });
        }
      }

      await fetchRegistrations();
      
      toast({
        title: "Status Updated",
        description: `Registration ${newStatus === 'verified' ? 'verified' : 'marked as pending'}${newStatus === 'verified' ? ' and confirmation email sent' : ''}`,
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update registration status",
        variant: "destructive"
      });
    }
  };

  const sendBulkEmail = async () => {
    try {
      const { data, error } = await supabase.functions.invoke('send-bulk-email', {
        body: {
          password: ADMIN_PASSWORD,
          subject: emailSubject,
          message: emailMessage,
          recipients: 'all'
        }
      });

      if (error) throw error;
      if (data.error) throw new Error(data.error);

      toast({
        title: "Bulk Email Sent",
        description: "Email sent to all registered users",
      });
      
      setShowBulkEmail(false);
      setEmailSubject('');
      setEmailMessage('');
    } catch (error) {
      console.error('Error sending bulk email:', error);
      toast({
        title: "Email Failed",
        description: "Failed to send bulk email",
        variant: "destructive"
      });
    }
  };

  const sendIndividualEmail = async () => {
    if (!selectedRegistration) return;
    
    try {
      const { data, error } = await supabase.functions.invoke('send-bulk-email', {
        body: {
          password: ADMIN_PASSWORD,
          subject: emailSubject,
          message: emailMessage,
          recipients: 'individual',
          email: selectedRegistration.email,
          fullName: selectedRegistration.full_name
        }
      });

      if (error) throw error;
      if (data.error) throw new Error(data.error);

      toast({
        title: "Email Sent",
        description: `Email sent to ${selectedRegistration.full_name}`,
      });
      
      setShowIndividualEmail(false);
      setEmailSubject('');
      setEmailMessage('');
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: "Email Failed",
        description: "Failed to send email",
        variant: "destructive"
      });
    }
  };

  const exportToCSV = () => {
    const headers = [
      'Full Name',
      'Email', 
      'Phone Number',
      'Course',
      'Amount',
      'MPesa Name',
      'MPesa Number',
      'MPesa Message',
      'Status',
      'Created At',
      'Verified At'
    ];

    const csvData = registrations.map(reg => [
      reg.full_name,
      reg.email,
      reg.phone_number,
      COURSE_NAMES[reg.course as keyof typeof COURSE_NAMES],
      reg.amount,
      reg.mpesa_name || '',
      reg.mpesa_number || '',
      reg.mpesa_message || '',
      reg.status,
      new Date(reg.created_at).toLocaleString(),
      reg.verified_at ? new Date(reg.verified_at).toLocaleString() : ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `registrations_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "CSV Exported",
      description: "Registration data exported successfully",
    });
  };

  const getStatusBadge = (status: string) => {
    if (status === 'verified') {
      return <Badge className="bg-success text-success-foreground"><CheckCircle className="w-3 h-3 mr-1" /> Verified</Badge>;
    }
    return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" /> Pending</Badge>;
  };

  const getStats = () => {
    const total = registrations.length;
    const verified = registrations.filter(r => r.status === 'verified').length;
    const pending = total - verified;
    const totalRevenue = registrations
      .filter(r => r.status === 'verified')
      .reduce((sum, r) => sum + r.amount, 0);

    return { total, verified, pending, totalRevenue };
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gradient-card shadow-strong">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-gradient-primary rounded-full mx-auto flex items-center justify-center shadow-primary mb-4">
              <Shield className="w-8 h-8 text-primary-foreground" />
            </div>
            <CardTitle className="text-2xl font-bold">Admin Access</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Input
                type="password"
                placeholder="Enter admin password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
              />
            </div>
            <Button onClick={handleLogin} className="w-full">
              Access Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const stats = getStats();

  return (
    <div className="min-h-screen bg-muted/30 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center py-8">
          <h1 className="text-4xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            Course Registration Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">Manage student registrations and payment verifications</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-card shadow-medium">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <Users className="w-8 h-8 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Registrations</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-card shadow-medium">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-8 h-8 text-success" />
                <div>
                  <p className="text-sm text-muted-foreground">Verified</p>
                  <p className="text-2xl font-bold">{stats.verified}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-card shadow-medium">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <Clock className="w-8 h-8 text-warning" />
                <div>
                  <p className="text-sm text-muted-foreground">Pending</p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-card shadow-medium">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <DollarSign className="w-8 h-8 text-accent" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl font-bold">KSH {stats.totalRevenue.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="registrations" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="registrations">Registrations</TabsTrigger>
            <TabsTrigger value="theme">Theme Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="registrations" className="space-y-6">
            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4">
              <Button onClick={fetchRegistrations} variant="outline">
                Refresh Data
              </Button>
              <Button onClick={() => setShowBulkEmail(true)} variant="outline">
                <Mail className="w-4 h-4 mr-2" />
                Send Bulk Email
              </Button>
              <Button onClick={exportToCSV} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export CSV
              </Button>
            </div>

            {/* Registrations Table */}
            <Card className="bg-gradient-card shadow-medium">
              <CardHeader>
                <CardTitle>Registrations</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="all" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="all">All ({stats.total})</TabsTrigger>
                    <TabsTrigger value="pending">Pending ({stats.pending})</TabsTrigger>
                    <TabsTrigger value="verified">Verified ({stats.verified})</TabsTrigger>
                  </TabsList>
                  
                  {['all', 'pending', 'verified'].map((filter) => (
                    <TabsContent key={filter} value={filter}>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Student</TableHead>
                              <TableHead>Course</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Date</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {loading ? (
                              <TableRow>
                                <TableCell colSpan={6} className="text-center py-8">
                                  Loading registrations...
                                </TableCell>
                              </TableRow>
                            ) : (
                              registrations
                                .filter(r => filter === 'all' || r.status === filter)
                                .map((registration) => (
                                  <TableRow key={registration.id}>
                                    <TableCell>
                                      <div>
                                        <div className="font-medium">{registration.full_name}</div>
                                        <div className="text-sm text-muted-foreground">{registration.email}</div>
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <div>
                                        <div className="font-medium">
                                          {COURSE_NAMES[registration.course as keyof typeof COURSE_NAMES]}
                                        </div>
                                      </div>
                                    </TableCell>
                                    <TableCell className="font-medium">
                                      KSH {registration.amount.toLocaleString()}
                                    </TableCell>
                                    <TableCell>
                                      {getStatusBadge(registration.status)}
                                    </TableCell>
                                    <TableCell>
                                      {new Date(registration.created_at).toLocaleDateString()}
                                    </TableCell>
                                    <TableCell>
                                      <div className="flex space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => {
                                            setSelectedRegistration(registration);
                                            setShowDetails(true);
                                          }}
                                        >
                                          <Eye className="w-4 h-4" />
                                        </Button>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => {
                                            setSelectedRegistration(registration);
                                            setShowIndividualEmail(true);
                                          }}
                                        >
                                          <Mail className="w-4 h-4" />
                                        </Button>
                                        {registration.status === 'pending' ? (
                                          <Button
                                            size="sm"
                                            onClick={() => updateStatus(registration.id, 'verified')}
                                            className="bg-success hover:bg-success/90"
                                          >
                                            <CheckCircle className="w-4 h-4 mr-1" />
                                            Verify
                                          </Button>
                                        ) : (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => updateStatus(registration.id, 'pending')}
                                          >
                                            <Clock className="w-4 h-4 mr-1" />
                                            Pending
                                          </Button>
                                        )}
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="theme" className="space-y-6">
            <Card className="bg-gradient-card shadow-medium">
              <CardHeader>
                <CardTitle>Color Theme Settings</CardTitle>
                <p className="text-muted-foreground">Choose feminine colors for your registration form</p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Background Colors</h4>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-rose-100 to-pink-100 text-rose-900">
                        Nude Rose
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-pink-100 to-rose-200 text-pink-900">
                        Soft Pink
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-orange-100 to-amber-100 text-amber-900">
                        Warm Peach
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-purple-100 to-pink-100 text-purple-900">
                        Lavender Blush
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-3">Accent Colors</h4>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-amber-200 to-orange-200 text-amber-900">
                        Golden Honey
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-rose-200 to-pink-300 text-rose-900">
                        Rose Gold
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-orange-200 to-red-200 text-orange-900">
                        Coral Sunset
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gradient-to-r from-purple-200 to-pink-200 text-purple-900">
                        Mauve Dreams
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-3">Text Colors</h4>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start bg-gray-800 text-white">
                        Charcoal Black
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-gray-700 text-white">
                        Deep Brown
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-rose-800 text-white">
                        Burgundy
                      </Button>
                      <Button variant="outline" className="w-full justify-start bg-purple-800 text-white">
                        Plum Purple
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <Button className="w-full md:w-auto">
                    Apply Color Changes
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Registration Details Modal */}
        <Dialog open={showDetails} onOpenChange={setShowDetails}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Registration Details</DialogTitle>
              <DialogDescription>
                Complete information for this registration
              </DialogDescription>
            </DialogHeader>
            
            {selectedRegistration && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Users className="w-4 h-4 mr-2" />
                      Student Information
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div><span className="font-medium">Name:</span> {selectedRegistration.full_name}</div>
                      <div className="flex items-center">
                        <Mail className="w-4 h-4 mr-2" />
                        {selectedRegistration.email}
                      </div>
                      <div className="flex items-center">
                        <Phone className="w-4 h-4 mr-2" />
                        {selectedRegistration.phone_number}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Course Details</h4>
                    <div className="space-y-2 text-sm">
                      <div><span className="font-medium">Course:</span> {COURSE_NAMES[selectedRegistration.course as keyof typeof COURSE_NAMES]}</div>
                      <div><span className="font-medium">Amount:</span> KSH {selectedRegistration.amount.toLocaleString()}</div>
                      <div><span className="font-medium">Status:</span> {getStatusBadge(selectedRegistration.status)}</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">MPesa Payment Details</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Payer Name:</span> {selectedRegistration.mpesa_name}</div>
                    <div><span className="font-medium">Phone Number:</span> {selectedRegistration.mpesa_number}</div>
                    <div>
                      <span className="font-medium">Confirmation Message:</span>
                      <div className="mt-1 p-3 bg-muted rounded border text-xs font-mono whitespace-pre-wrap">
                        {selectedRegistration.mpesa_message}
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Timestamps</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Submitted:</span> {new Date(selectedRegistration.created_at).toLocaleString()}</div>
                    {selectedRegistration.verified_at && (
                      <div><span className="font-medium">Verified:</span> {new Date(selectedRegistration.verified_at).toLocaleString()}</div>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4 border-t">
                  {selectedRegistration.status === 'pending' ? (
                    <Button
                      onClick={() => {
                        updateStatus(selectedRegistration.id, 'verified');
                        setShowDetails(false);
                      }}
                      className="bg-success hover:bg-success/90"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Verify Payment
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={() => {
                        updateStatus(selectedRegistration.id, 'pending');
                        setShowDetails(false);
                      }}
                    >
                      <Clock className="w-4 h-4 mr-2" />
                      Mark as Pending
                    </Button>
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Bulk Email Modal */}
        <Dialog open={showBulkEmail} onOpenChange={setShowBulkEmail}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Send Bulk Email</DialogTitle>
              <DialogDescription>
                Send an email to all registered users
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Subject</label>
                <Input
                  value={emailSubject}
                  onChange={(e) => setEmailSubject(e.target.value)}
                  placeholder="Email subject"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Message</label>
                <Textarea
                  value={emailMessage}
                  onChange={(e) => setEmailMessage(e.target.value)}
                  placeholder="Email message"
                  rows={6}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowBulkEmail(false)}>
                  Cancel
                </Button>
                <Button onClick={sendBulkEmail}>
                  <Send className="w-4 h-4 mr-2" />
                  Send to All
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Individual Email Modal */}
        <Dialog open={showIndividualEmail} onOpenChange={setShowIndividualEmail}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Send Individual Email</DialogTitle>
              <DialogDescription>
                Send an email to {selectedRegistration?.full_name}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Subject</label>
                <Input
                  value={emailSubject}
                  onChange={(e) => setEmailSubject(e.target.value)}
                  placeholder="Email subject"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Message</label>
                <Textarea
                  value={emailMessage}
                  onChange={(e) => setEmailMessage(e.target.value)}
                  placeholder="Email message"
                  rows={6}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowIndividualEmail(false)}>
                  Cancel
                </Button>
                <Button onClick={sendIndividualEmail}>
                  <Send className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

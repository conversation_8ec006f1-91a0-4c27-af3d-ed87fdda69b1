@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Feminine Beauty Course Registration Design Tokens */
    --background: 17 27% 96%;
    --foreground: 345 8% 25%;

    --card: 17 27% 98%;
    --card-foreground: 345 8% 25%;

    --popover: 17 27% 98%;
    --popover-foreground: 345 8% 25%;

    /* Nude Rose Primary */
    --primary: 14 44% 84%;
    --primary-foreground: 345 8% 25%;
    --primary-dark: 14 44% 74%;
    --primary-light: 14 44% 94%;

    /* Soft Pink Secondary */
    --secondary: 340 60% 85%;
    --secondary-foreground: 345 8% 25%;
    --secondary-dark: 340 60% 75%;
    --secondary-light: 340 60% 95%;

    /* Warm Beige Accent */
    --accent: 30 35% 78%;
    --accent-foreground: 345 8% 25%;
    --accent-dark: 30 35% 68%;
    --accent-light: 30 35% 88%;

    --muted: 217 10% 96%;
    --muted-foreground: 217 10% 45%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 217 10% 89%;
    --input: 217 10% 89%;
    --ring: 213 94% 68%;

    /* Status Colors */
    --success: 142 69% 58%;
    --success-foreground: 0 0% 100%;
    --warning: 45 93% 58%;
    --warning-foreground: 217 19% 27%;
    --pending: 217 10% 45%;
    --pending-foreground: 0 0% 100%;

    /* Feminine Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-dark)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--secondary-dark)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
    --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--background)) 100%);

    /* Shadows */
    --shadow-soft: 0 2px 8px hsl(217 19% 27% / 0.04);
    --shadow-medium: 0 4px 16px hsl(217 19% 27% / 0.08);
    --shadow-strong: 0 8px 32px hsl(217 19% 27% / 0.12);
    --shadow-primary: 0 4px 16px hsl(var(--primary) / 0.2);
    --shadow-secondary: 0 4px 16px hsl(var(--secondary) / 0.2);

    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode - keeping existing structure */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 213 94% 78%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-dark: 213 94% 68%;
    --primary-light: 213 94% 88%;

    --secondary: 142 69% 68%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-dark: 142 69% 58%;
    --secondary-light: 142 69% 78%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 45 93% 68%;
    --accent-foreground: 222.2 84% 4.9%;
    --accent-dark: 45 93% 58%;
    --accent-light: 45 93% 78%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
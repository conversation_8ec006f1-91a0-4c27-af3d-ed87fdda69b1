
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface BulkEmailRequest {
  password: string;
  subject: string;
  message: string;
  recipients: 'all' | 'individual';
  email?: string;
  fullName?: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { password, subject, message, recipients, email, fullName }: BulkEmailRequest = await req.json();

    // Verify admin password
    const ADMIN_PASSWORD = 're_4fmn1EeW_N5t2AM1CkbCZHCfHvpRAJwq6';
    if (password !== ADMIN_PASSWORD) {
      return new Response(
        JSON.stringify({ error: 'Invalid password' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('Sending bulk email:', { subject, recipients, email, fullName });

    if (recipients === 'individual' && email && fullName) {
      // Send to individual
      const emailResponse = await resend.emails.send({
        from: "ShatAfrika Beauty MaterClass <<EMAIL>>",
        to: [email],
        subject: subject,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #3b82f6, #10b981); padding: 30px; border-radius: 12px; margin-bottom: 30px;">
              <h1 style="color: white; margin: 0; text-align: center; font-size: 28px;">${subject}</h1>
            </div>
            
            <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
              <h2 style="color: #1e293b; margin-top: 0;">Hello ${fullName}!</h2>
              <div style="color: #475569; line-height: 1.6; font-size: 16px; white-space: pre-wrap;">${message}</div>
            </div>
            
            <div style="text-align: center; padding: 20px; background: #f1f5f9; border-radius: 8px;">
              <p style="color: #475569; margin: 0; font-size: 14px;">
                Questions? Contact us at 
                <a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none;">
                  <EMAIL>
                </a>
              </p>
              <p style="color: #64748b; margin: 10px 0 0 0; font-size: 12px;">
                ShatAfrika Beauty Masterclss- ShatAfrika Beauty MaterClass
              </p>
            </div>
          </div>
        `,
      });

      console.log("Individual email sent successfully:", emailResponse);

      return new Response(JSON.stringify(emailResponse), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    } else if (recipients === 'all') {
      // Get all registrations
      const supabaseAdmin = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
      );

      const { data: registrations, error } = await supabaseAdmin
        .from('registrations')
        .select('email, full_name');

      if (error) throw error;

      // Send to all users
      const emailPromises = registrations.map(async (registration: any) => {
        return resend.emails.send({
          from: "ShatAfrika Beauty MaterClass <<EMAIL>>",
          to: [registration.email],
          subject: subject,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: linear-gradient(135deg, #3b82f6, #10b981); padding: 30px; border-radius: 12px; margin-bottom: 30px;">
                <h1 style="color: white; margin: 0; text-align: center; font-size: 28px;">${subject}</h1>
              </div>
              
              <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
                <h2 style="color: #1e293b; margin-top: 0;">Hello ${registration.full_name}!</h2>
                <div style="color: #475569; line-height: 1.6; font-size: 16px; white-space: pre-wrap;">${message}</div>
              </div>
              
              <div style="text-align: center; padding: 20px; background: #f1f5f9; border-radius: 8px;">
                <p style="color: #475569; margin: 0; font-size: 14px;">
                  Questions? Contact us at 
                  <a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none;">
                    <EMAIL>
                  </a>
                </p>
                <p style="color: #64748b; margin: 10px 0 0 0; font-size: 12px;">
                  ShatAfrika Beauty Masterclss- Beauty Masterclass
                </p>
              </div>
            </div>
          `,
        });
      });

      const results = await Promise.allSettled(emailPromises);
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      console.log(`Bulk email results: ${successful} successful, ${failed} failed`);

      return new Response(JSON.stringify({ 
        success: true, 
        sent: successful, 
        failed: failed 
      }), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }

    return new Response(
      JSON.stringify({ error: 'Invalid recipients type' }),
      {
        status: 400,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error("Error in send-bulk-email function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);

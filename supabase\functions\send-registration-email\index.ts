import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface EmailRequest {
  email: string;
  fullName: string;
  course: string;
  type: 'acknowledgment' | 'verification';
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { email, fullName, course, type }: EmailRequest = await req.json();

    console.log('Sending email:', { email, fullName, course, type });

    let subject: string;
    let htmlContent: string;

    if (type === 'acknowledgment') {
      subject = "Course Registration Received - Under Review";
      htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #3b82f6, #10b981); padding: 30px; border-radius: 12px; margin-bottom: 30px;">
            <h1 style="color: white; margin: 0; text-align: center; font-size: 28px;">Registration Received! ✨</h1>
          </div>
          
          <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #1e293b; margin-top: 0;">Hello ${fullName}!</h2>
            <p style="color: #475569; line-height: 1.6; font-size: 16px;">
              Thank you for registering for the <strong>${course}</strong>! We've successfully received your registration details and payment confirmation.
            </p>
            <p style="color: #475569; line-height: 1.6; font-size: 16px;">
              <strong>What happens next?</strong>
            </p>
            <ul style="color: #475569; line-height: 1.6; font-size: 16px;">
              <li>Our team will verify your MPesa payment details</li>
              <li>You'll receive a confirmation email once verified (usually within 24 hours)</li>
              <li>Course details and schedules will be shared upon verification</li>
            </ul>
          </div>
          
          <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin-bottom: 25px;">
            <p style="margin: 0; color: #92400e; font-weight: 500;">
              💡 <strong>Important:</strong> Please keep this email for your records. Your registration is currently under review.
            </p>
          </div>
          
          <div style="text-align: center; padding: 20px; background: #f1f5f9; border-radius: 8px;">
            <p style="color: #475569; margin: 0; font-size: 14px;">
              Questions? Contact us at 
              <a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none;">
                <EMAIL>
              </a>
            </p>
            <p style="color: #64748b; margin: 10px 0 0 0; font-size: 12px;">
              ShatAfrika Beauty Masterclss- Beauty Masterclass
            </p>
          </div>
        </div>
      `;
    } else {
      subject = "Payment Verified - Welcome to Your Masterclass! 🎉";
      htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #10b981, #059669); padding: 30px; border-radius: 12px; margin-bottom: 30px;">
            <h1 style="color: white; margin: 0; text-align: center; font-size: 28px;">Payment Verified! 🎉</h1>
            <p style="color: rgba(255,255,255,0.9); text-align: center; margin: 10px 0 0 0;">Your spot is confirmed</p>
          </div>
          
          <div style="background: #f0fdf4; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #166534; margin-top: 0;">Congratulations ${fullName}!</h2>
            <p style="color: #15803d; line-height: 1.6; font-size: 16px;">
              Your payment has been successfully verified for the <strong>${course}</strong>. 
              Your registration is now complete and your spot in the ShatAfrika Beauty Masterclass is confirmed!
            </p>
          </div>
          
          <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="color: #1e293b; margin-top: 0;">What's Next?</h3>
            <ul style="color: #475569; line-height: 1.6; font-size: 16px;">
              <li>Course materials and schedule will be shared soon</li>
              <li>You'll receive location details and timing information</li>
              <li>Prepare to transform your beauty skills! ✨</li>
            </ul>
          </div>
          
          <div style="background: #dbeafe; border-left: 4px solid #3b82f6; padding: 15px; margin-bottom: 25px;">
            <p style="margin: 0; color: #1e40af; font-weight: 500;">
              📚 <strong>Welcome aboard!</strong> We're excited to have you join our masterclass family.
            </p>
          </div>
          
          <div style="text-align: center; padding: 20px; background: #f1f5f9; border-radius: 8px;">
            <p style="color: #475569; margin: 0; font-size: 14px;">
              Questions? Contact us at 
              <a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none;">
                <EMAIL>
              </a>
            </p>
            <p style="color: #64748b; margin: 10px 0 0 0; font-size: 12px;">
              ShatAfrika Beauty Masterclss- Beauty Masterclass
            </p>
          </div>
        </div>
      `;
    }

    const emailResponse = await resend.emails.send({
      from: "ShatAfrika Beauty MaterClass <<EMAIL>>",
      to: [email],
      subject: subject,
      html: htmlContent,
    });

    console.log("Email sent successfully:", emailResponse);

    return new Response(JSON.stringify(emailResponse), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-registration-email function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);